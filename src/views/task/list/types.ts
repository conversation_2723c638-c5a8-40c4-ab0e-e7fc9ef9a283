import { TASK_TYPES, TASK_STATUS } from './constants'

export type TaskType = (typeof TASK_TYPES)[keyof typeof TASK_TYPES]
export type TaskStatus = (typeof TASK_STATUS)[keyof typeof TASK_STATUS]

export interface Task {
  id: number
  name: string
  taskType: TaskType
  taskDataId: number
  status: TaskStatus
  aiModelId: string
  aiModelName: string
  alarmNoticeUserId: string
  reason: string
  creator: string
  createTime: string
  updater: string
  deleted: boolean
  updateTime: string
  tenantId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  id?: number
  name?: string
  taskType?: TaskType
  status?: TaskStatus
  aiModelId?: string
  createTime?: string[]
}

export interface CreateTaskForm {
  name: string
  taskType: TaskType | undefined
  taskDataId?: number
  aiModelId?: string
  alarmNoticeUserId?: string
}
