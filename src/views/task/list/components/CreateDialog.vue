<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建任务"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入任务名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务类型" prop="taskType">
        <el-select
          v-model="formData.taskType"
          placeholder="请选择任务类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="(label, value) in TASK_TYPE_LABELS"
            :key="value"
            :label="label"
            :value="Number(value)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据集ID" prop="taskDataId">
        <el-input-number
          v-model="formData.taskDataId"
          placeholder="请输入数据集或视频流ID"
          style="width: 100%"
          :min="1"
        />
      </el-form-item>

      <el-form-item label="AI模型ID" prop="aiModelId">
        <el-input v-model="formData.aiModelId" placeholder="请输入AI模型ID" clearable />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createTask } from '@/api/alg/task'
import { TASK_TYPE_LABELS } from '../constants'
import type { CreateTaskForm } from '../types'

defineOptions({ name: 'CreateTaskDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<CreateTaskForm>({
  name: '',
  taskType: undefined,
  taskDataId: undefined,
  aiModelId: ''
})

const formRules: FormRules<CreateTaskForm> = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 50, message: '任务名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  taskType: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  taskDataId: [{ required: true, message: '请输入数据集或视频流ID', trigger: 'blur' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    taskType: undefined,
    taskDataId: undefined,
    aiModelId: ''
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      name: formData.name,
      taskType: formData.taskType!,
      taskDataId: formData.taskDataId!,
      ...(formData.aiModelId && { aiModelId: formData.aiModelId })
    }

    await createTask(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建任务失败:', error)
  } finally {
    loading.value = false
  }
}

const fillWithTestData = () => {
  formData.name = '测试任务 ' + new Date().getTime()
  formData.taskType = 0
  formData.taskDataId = 1
  formData.aiModelId = '1'
}

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
