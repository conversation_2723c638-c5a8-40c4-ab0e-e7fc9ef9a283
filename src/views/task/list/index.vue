<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">任务列表</h5>
        <el-button type="primary" @click="handleCreate">新建任务</el-button>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table :data="taskList" v-loading="loading" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="任务名称" min-width="150" />
          <el-table-column prop="taskType" label="任务类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.taskType === 0 ? 'primary' : 'success'">
                {{ row.taskType === 0 ? '数据集' : '视频流' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="aiModelName" label="AI模型" min-width="120" />
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(new Date(row.createTime)) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleStart(row)">执行</el-button>
              <el-button link type="primary" @click="handleUpdate(row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 创建任务弹窗 -->
    <CreateDialog ref="createDialogRef" @success="handleCreateSuccess" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Filter from './components/Filter.vue'
import CreateDialog from './components/CreateDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES, TASK_STATUS_LABELS, TASK_STATUS_COLORS } from './constants'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'Task' })

const createDialogRef = ref()

const {
  loading,
  total,
  taskList,
  queryParams,
  getList,
  handleQuery,
  handleUpdate,
  handleDelete,
  handleStart
} = useList()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.name = undefined
  queryParams.taskType = undefined
  queryParams.status = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  createDialogRef.value?.open()
}

/** 创建成功回调 */
const handleCreateSuccess = () => {
  getList()
}

/** 获取状态标签 */
const getStatusLabel = (status: number) => {
  return TASK_STATUS_LABELS[status] || '未知'
}

/** 获取状态颜色 */
const getStatusColor = (status: number) => {
  return TASK_STATUS_COLORS[status] || 'info'
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  height: calc(100vh - 158px);
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding-left: 20px;
  display: flex;
  align-items: center;
  height: 72px;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
